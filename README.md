---
title: GAIA Agent - Competition Submission
emoji: 🕵🏻‍♂️
colorFrom: indigo
colorTo: indigo
sdk: gradio
sdk_version: 5.25.2
app_file: app.py
pinned: false
hf_oauth: true
hf_oauth_expiration_minutes: 480
---

# 🕵🏻‍♂️ GAIA Agent - Advanced AI Agent for GAIA Benchmark

This is a sophisticated AI agent designed to compete in the GAIA (General AI Assistant) benchmark competition. The agent combines multiple AI capabilities to solve complex, multi-step reasoning problems.

## 🚀 Features

### Core Capabilities
- **🧠 LangGraph-based Reasoning**: Multi-step planning and execution
- **🔍 Research Tools**: Web search, Wikipedia, and ArXiv integration
- **💻 Code Execution**: Multi-language support (Python, Bash, SQL, C, Java)
- **🖼️ Image Processing**: OCR, analysis, and transformation
- **📊 Data Analysis**: CSV/Excel file processing and analysis
- **🧮 Mathematical Operations**: Basic and advanced calculations

### LLM Integration
- **Primary**: Anthropic Claude 3.5 Sonnet (high performance)
- **Fallback**: Hugging Face models (free alternative)

## 🛠️ Setup Instructions

### 1. <PERSON><PERSON> and Configure
```bash
git clone <your-repo-url>
cd Final_Assignment_Template
```

### 2. Set Up Environment Variables
Copy the example environment file and add your API keys:
```bash
cp .env.example .env
```

Edit `.env` with your API keys:
- `ANTHROPIC_API_KEY`: Your Anthropic API key (recommended)
- `HUGGINGFACE_API_TOKEN`: Your Hugging Face token
- `TAVILY_API_KEY`: For web search functionality (optional)

### 3. Install Dependencies
```bash
pip install -r requirements.txt
```

### 4. Run the Application
```bash
python app.py
```

## 🏆 Competition Performance

This agent is designed to achieve competitive performance on GAIA Level 1 questions by:
- Thorough research using multiple information sources
- Step-by-step problem decomposition
- Code execution for calculations and data processing
- Image analysis for visual questions
- Precise answer formatting for exact match scoring

## 📁 Project Structure

```
├── agent.py              # Main agent implementation
├── app.py                # Gradio interface
├── code_interpreter.py   # Multi-language code execution
├── image_processing.py   # Image utilities
├── system_prompt.txt     # Agent instructions
├── requirements.txt      # Dependencies
└── .env.example         # Environment template
```

## 🔧 Customization

The agent is highly modular and can be customized by:
- Adding new tools in `agent.py`
- Modifying the system prompt in `system_prompt.txt`
- Adjusting LLM providers in the `build_graph()` function
- Extending code execution capabilities in `code_interpreter.py`

## 📊 Usage

1. **Web Interface**: Run the Gradio app and use the web interface
2. **Direct Usage**: Import and use the `GAIAAgent` class directly
3. **API Integration**: Integrate with the GAIA competition API

## 🤝 Contributing

Feel free to improve the agent by:
- Adding new tools and capabilities
- Optimizing performance
- Enhancing error handling
- Improving documentation

## 📄 License

This project is open source and available under the MIT License.