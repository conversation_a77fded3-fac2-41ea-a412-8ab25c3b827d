import os
import gradio as gr
import requests
import pandas as pd
from agent import GAIAAgent

# Constants
DEFAULT_API_URL = "https://agents-course-unit4-scoring.hf.space"

def test_agent_locally():
    """Test the agent with a simple question locally"""
    try:
        agent = GAIAAgent(provider="anthropic")
        question = "What is 25 * 4 + 10?"
        result = agent(question)
        return f"✅ Agent working! Question: {question}\nAnswer: {result}"
    except Exception as e:
        return f"❌ Agent test failed: {str(e)}"

def fetch_sample_question():
    """Fetch a sample question from the API"""
    try:
        api_url = DEFAULT_API_URL
        questions_url = f"{api_url}/random-question"
        
        response = requests.get(questions_url, timeout=15)
        response.raise_for_status()
        question_data = response.json()
        
        return f"Sample Question:\n{question_data.get('question', 'No question found')}"
    except Exception as e:
        return f"Error fetching question: {str(e)}"

def test_agent_with_sample():
    """Test agent with a sample question from the API"""
    try:
        # Fetch a sample question
        api_url = DEFAULT_API_URL
        questions_url = f"{api_url}/random-question"
        
        response = requests.get(questions_url, timeout=15)
        response.raise_for_status()
        question_data = response.json()
        
        question = question_data.get('question', '')
        if not question:
            return "No question received from API"
        
        # Test with our agent
        agent = GAIAAgent(provider="anthropic")
        result = agent(question)
        
        return f"Question: {question[:200]}...\n\nAgent Answer: {result}"
        
    except Exception as e:
        return f"Error testing with sample question: {str(e)}"

# Build Gradio Interface
with gr.Blocks(title="GAIA Agent - Local Testing") as demo:
    gr.Markdown("# 🕵🏻‍♂️ GAIA Agent - Local Testing Interface")
    gr.Markdown("""
    **Test your GAIA Agent locally before deploying to Hugging Face Space**
    
    This interface allows you to:
    - Test basic agent functionality
    - Fetch sample questions from the competition API
    - Test the agent with real GAIA questions
    """)
    
    with gr.Row():
        with gr.Column():
            test_basic_btn = gr.Button("🧪 Test Basic Agent", variant="primary")
            fetch_question_btn = gr.Button("📥 Fetch Sample Question")
            test_sample_btn = gr.Button("🚀 Test with Sample Question", variant="secondary")
        
        with gr.Column():
            output = gr.Textbox(
                label="Results", 
                lines=10, 
                interactive=False,
                placeholder="Click a button to test the agent..."
            )
    
    # Button actions
    test_basic_btn.click(fn=test_agent_locally, outputs=output)
    fetch_question_btn.click(fn=fetch_sample_question, outputs=output)
    test_sample_btn.click(fn=test_agent_with_sample, outputs=output)
    
    gr.Markdown("""
    ---
    **Next Steps:**
    1. If tests pass, deploy to your Hugging Face Space
    2. Set environment variables in Space settings:
       - `ANTHROPIC_API_KEY`: Your Anthropic API key
       - `HUGGINGFACE_API_TOKEN`: Your HF token
    3. Use the main `app.py` for competition submission
    """)

if __name__ == "__main__":
    print("🚀 Starting GAIA Agent Local Testing Interface...")
    demo.launch(debug=True, share=False)
