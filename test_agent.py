#!/usr/bin/env python3
"""
Test script for the GAIA Agent
"""

import os
from dotenv import load_dotenv

# Load environment variables
load_dotenv()

def test_basic_functionality():
    """Test basic agent functionality without API calls"""
    print("🧪 Testing basic agent functionality...")
    
    # Test imports
    try:
        from agent import GAIAAgent
        print("✅ Agent import successful")
    except ImportError as e:
        print(f"❌ Agent import failed: {e}")
        return False
    
    # Test code interpreter
    try:
        from code_interpreter import CodeInterpreter
        interpreter = CodeInterpreter()
        result = interpreter.execute_code("print('Hello, World!')", "python")
        if result["status"] == "success":
            print("✅ Code interpreter working")
        else:
            print(f"❌ Code interpreter failed: {result['stderr']}")
    except Exception as e:
        print(f"❌ Code interpreter test failed: {e}")
    
    # Test image processing
    try:
        from image_processing import save_image, encode_image, decode_image
        from PIL import Image
        import numpy as np
        
        # Create a simple test image
        test_img = Image.fromarray(np.random.randint(0, 255, (100, 100, 3), dtype=np.uint8))
        path = save_image(test_img)
        encoded = encode_image(path)
        decoded = decode_image(encoded)
        print("✅ Image processing working")
    except Exception as e:
        print(f"❌ Image processing test failed: {e}")
    
    return True

def test_mathematical_tools():
    """Test mathematical operations"""
    print("\n🧮 Testing mathematical tools...")
    
    try:
        from agent import add, multiply, divide, square_root
        
        # Test basic operations using invoke method
        assert add.invoke({"a": 2, "b": 3}) == 5
        assert multiply.invoke({"a": 4, "b": 5}) == 20
        assert divide.invoke({"a": 10, "b": 2}) == 5
        assert abs(square_root.invoke({"a": 16}) - 4.0) < 0.001
        
        print("✅ Mathematical tools working correctly")
        return True
    except Exception as e:
        print(f"❌ Mathematical tools test failed: {e}")
        return False

def test_with_api_keys():
    """Test agent with API keys if available"""
    print("\n🔑 Testing with API keys...")
    
    anthropic_key = os.getenv("ANTHROPIC_API_KEY")
    hf_token = os.getenv("HUGGINGFACE_API_TOKEN")
    
    if not anthropic_key and not hf_token:
        print("⚠️  No API keys found. Skipping API tests.")
        print("   Set ANTHROPIC_API_KEY or HUGGINGFACE_API_TOKEN to test with LLMs")
        return True
    
    try:
        from agent import GAIAAgent
        
        if anthropic_key:
            print("Testing with Anthropic Claude...")
            agent = GAIAAgent(provider="anthropic")
            response = agent("What is 2 + 2?")
            print(f"Response: {response}")
            print("✅ Anthropic provider working")
        
        elif hf_token:
            print("Testing with Hugging Face...")
            agent = GAIAAgent(provider="huggingface")
            response = agent("What is 2 + 2?")
            print(f"Response: {response}")
            print("✅ Hugging Face provider working")
            
        return True
        
    except Exception as e:
        print(f"❌ API test failed: {e}")
        return False

def main():
    """Run all tests"""
    print("🚀 Starting GAIA Agent Tests\n")
    
    # Run tests
    basic_ok = test_basic_functionality()
    math_ok = test_mathematical_tools()
    api_ok = test_with_api_keys()
    
    # Summary
    print("\n" + "="*50)
    print("📊 Test Summary:")
    print(f"   Basic Functionality: {'✅ PASS' if basic_ok else '❌ FAIL'}")
    print(f"   Mathematical Tools:  {'✅ PASS' if math_ok else '❌ FAIL'}")
    print(f"   API Integration:     {'✅ PASS' if api_ok else '❌ FAIL'}")
    
    if basic_ok and math_ok:
        print("\n🎉 Agent is ready for competition!")
        print("   Make sure to set your API keys in .env file")
    else:
        print("\n⚠️  Some tests failed. Please check the errors above.")

if __name__ == "__main__":
    main()
